{% load static %}

<div class="glassmorphism-card p-6">
    <!-- Enhanced Search and Filter Section -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
        <div class="flex-1 max-w-md">
            <div class="relative">
                <input type="text" 
                       id="message-search" 
                       placeholder="Search messages..." 
                       value="{{ search_query }}"
                       class="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-300 bg-white/80 backdrop-blur-sm"
                       hx-get="{% url 'core:admin_message_search' %}"
                       hx-target="#message-tab-content"
                       hx-trigger="keyup changed delay:300ms"
                       hx-include="[name='status'], [name='message_type'], [name='target_audience']">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-search text-gray-400"></i>
                </div>
            </div>
        </div>
        
        <div class="flex flex-wrap gap-3">
            <!-- Status Filter -->
            <select name="status" 
                    class="px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent bg-white/80 backdrop-blur-sm"
                    hx-get="{% url 'core:admin_message_list_tab' %}"
                    hx-target="#message-tab-content"
                    hx-trigger="change"
                    hx-include="[name='message_type'], [name='target_audience'], #message-search">
                <option value="">All Status</option>
                {% for value, label in status_choices %}
                    <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>{{ label }}</option>
                {% endfor %}
            </select>
            
            <!-- Message Type Filter -->
            <select name="message_type" 
                    class="px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent bg-white/80 backdrop-blur-sm"
                    hx-get="{% url 'core:admin_message_list_tab' %}"
                    hx-target="#message-tab-content"
                    hx-trigger="change"
                    hx-include="[name='status'], [name='target_audience'], #message-search">
                <option value="">All Types</option>
                {% for value, label in message_type_choices %}
                    <option value="{{ value }}" {% if message_type_filter == value %}selected{% endif %}>{{ label }}</option>
                {% endfor %}
            </select>
            
            <!-- Target Audience Filter -->
            <select name="target_audience" 
                    class="px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent bg-white/80 backdrop-blur-sm"
                    hx-get="{% url 'core:admin_message_list_tab' %}"
                    hx-target="#message-tab-content"
                    hx-trigger="change"
                    hx-include="[name='status'], [name='message_type'], #message-search">
                <option value="">All Audiences</option>
                {% for value, label in target_audience_choices %}
                    <option value="{{ value }}" {% if target_audience_filter == value %}selected{% endif %}>{{ label }}</option>
                {% endfor %}
            </select>
            
            <!-- Refresh Button -->
            <button class="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors duration-200 flex items-center"
                    hx-get="{% url 'core:admin_message_list_tab' %}"
                    hx-target="#message-tab-content"
                    hx-swap="innerHTML">
                <i class="fas fa-refresh mr-2"></i>
                Refresh
            </button>
        </div>
    </div>

    <!-- Messages Table -->
    <div class="overflow-x-auto">
        <table class="w-full">
            <thead>
                <tr class="border-b border-gray-200">
                    <th class="text-left py-3 px-4 font-semibold text-gray-700">Message</th>
                    <th class="text-left py-3 px-4 font-semibold text-gray-700">Type</th>
                    <th class="text-left py-3 px-4 font-semibold text-gray-700">Audience</th>
                    <th class="text-left py-3 px-4 font-semibold text-gray-700">Status</th>
                    <th class="text-left py-3 px-4 font-semibold text-gray-700">Performance</th>
                    <th class="text-left py-3 px-4 font-semibold text-gray-700">Created</th>
                    <th class="text-center py-3 px-4 font-semibold text-gray-700">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for message in messages %}
                <tr class="border-b border-gray-100 hover:bg-gray-50 transition-colors duration-200">
                    <td class="py-4 px-4">
                        <div class="flex items-start space-x-3">
                            {% if message.icon_class %}
                                <div class="w-10 h-10 rounded-lg flex items-center justify-center text-white"
                                     style="background: {{ message.background_color|default:'#dc2626' }};">
                                    <i class="{{ message.icon_class }}"></i>
                                </div>
                            {% endif %}
                            <div>
                                <h3 class="font-semibold text-gray-900 font-montserrat">{{ message.title }}</h3>
                                {% if message.excerpt %}
                                    <p class="text-sm text-gray-600 mt-1 font-raleway">{{ message.excerpt|truncatechars:80 }}</p>
                                {% endif %}
                                <div class="flex items-center space-x-2 mt-2">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                        {% if message.priority == 4 %}bg-red-100 text-red-800
                                        {% elif message.priority == 3 %}bg-orange-100 text-orange-800
                                        {% elif message.priority == 2 %}bg-blue-100 text-blue-800
                                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                                        Priority {{ message.priority }}
                                    </span>
                                    {% if message.show_as_popup %}
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                            <i class="fas fa-window-restore mr-1"></i>Popup
                                        </span>
                                    {% endif %}
                                    {% if message.show_as_banner %}
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <i class="fas fa-flag mr-1"></i>Banner
                                        </span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </td>
                    <td class="py-4 px-4">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {{ message.get_message_type_display }}
                        </span>
                    </td>
                    <td class="py-4 px-4">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            {{ message.get_target_audience_display }}
                        </span>
                    </td>
                    <td class="py-4 px-4">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            {% if message.status == 'active' %}bg-green-100 text-green-800
                            {% elif message.status == 'scheduled' %}bg-yellow-100 text-yellow-800
                            {% elif message.status == 'draft' %}bg-gray-100 text-gray-800
                            {% elif message.status == 'paused' %}bg-orange-100 text-orange-800
                            {% elif message.status == 'expired' %}bg-red-100 text-red-800
                            {% else %}bg-gray-100 text-gray-800{% endif %}">
                            {% if message.status == 'active' %}<i class="fas fa-play mr-1"></i>
                            {% elif message.status == 'scheduled' %}<i class="fas fa-clock mr-1"></i>
                            {% elif message.status == 'draft' %}<i class="fas fa-edit mr-1"></i>
                            {% elif message.status == 'paused' %}<i class="fas fa-pause mr-1"></i>
                            {% elif message.status == 'expired' %}<i class="fas fa-stop mr-1"></i>
                            {% endif %}
                            {{ message.get_status_display }}
                        </span>
                    </td>
                    <td class="py-4 px-4">
                        <div class="text-sm">
                            <div class="text-gray-900 font-semibold">{{ message.total_views|default:0 }} views</div>
                            <div class="text-gray-600">{{ message.total_clicks|default:0 }} clicks</div>
                            {% if message.total_views > 0 %}
                                <div class="text-xs text-gray-500">
                                    {{ message.click_through_rate|floatformat:1 }}% CTR
                                </div>
                            {% endif %}
                        </div>
                    </td>
                    <td class="py-4 px-4">
                        <div class="text-sm">
                            <div class="text-gray-900">{{ message.created_at|date:"M d, Y" }}</div>
                            <div class="text-gray-600">{{ message.created_at|time:"H:i" }}</div>
                            <div class="text-xs text-gray-500">by {{ message.created_by.get_full_name|default:message.created_by.username }}</div>
                        </div>
                    </td>
                    <td class="py-4 px-4">
                        <div class="flex items-center justify-center space-x-2">
                            <!-- Preview Button -->
                            <button class="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200"
                                    hx-get="{% url 'core:admin_message_preview_modal' message.id %}"
                                    hx-target="body"
                                    hx-swap="beforeend"
                                    title="Preview Message">
                                <i class="fas fa-eye"></i>
                            </button>
                            
                            <!-- Edit Button -->
                            <button class="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors duration-200"
                                    hx-get="{% url 'core:admin_message_edit_modal' message.id %}"
                                    hx-target="body"
                                    hx-swap="beforeend"
                                    title="Edit Message">
                                <i class="fas fa-edit"></i>
                            </button>
                            
                            <!-- Delete Button -->
                            <button class="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200"
                                    hx-delete="{% url 'core:admin_message_delete' message.id %}"
                                    hx-confirm="Are you sure you want to delete this message?"
                                    hx-target="closest tr"
                                    hx-swap="outerHTML"
                                    title="Delete Message">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="7" class="py-12 text-center">
                        <div class="text-gray-500">
                            <i class="fas fa-envelope text-4xl mb-4 text-gray-300"></i>
                            <p class="text-lg font-semibold mb-2">No messages found</p>
                            <p class="text-sm">
                                {% if search_query %}
                                    No messages match your search criteria.
                                {% else %}
                                    Create your first message to get started.
                                {% endif %}
                            </p>
                            {% if not search_query %}
                                <button class="mt-4 admin-request-add-btn"
                                        hx-get="{% url 'core:admin_message_create_modal' %}"
                                        hx-target="body"
                                        hx-swap="beforeend">
                                    <i class="fas fa-plus"></i>
                                    Create First Message
                                </button>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Enhanced Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="flex items-center justify-between mt-6 pt-6 border-t border-gray-200">
        <div class="text-sm text-gray-600">
            Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} messages
        </div>
        
        <div class="flex items-center space-x-2">
            {% if page_obj.has_previous %}
                <button class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                        hx-get="{% url 'core:admin_message_list_tab' %}?page={{ page_obj.previous_page_number }}"
                        hx-target="#message-tab-content"
                        hx-include="[name='status'], [name='message_type'], [name='target_audience'], #message-search">
                    <i class="fas fa-chevron-left"></i>
                </button>
            {% endif %}
            
            <span class="px-3 py-2 text-sm bg-harrier-red text-white rounded-lg">
                {{ page_obj.number }}
            </span>
            
            {% if page_obj.has_next %}
                <button class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                        hx-get="{% url 'core:admin_message_list_tab' %}?page={{ page_obj.next_page_number }}"
                        hx-target="#message-tab-content"
                        hx-include="[name='status'], [name='message_type'], [name='target_audience'], #message-search">
                    <i class="fas fa-chevron-right"></i>
                </button>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>

<style>
    /* Additional styles for message list */
    .admin-request-add-btn {
        background: linear-gradient(135deg, var(--harrier-red) 0%, var(--harrier-dark) 100%);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        display: inline-flex;
        align-items: center;
        text-decoration: none;
        font-family: 'Montserrat', sans-serif;
        box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
    }

    .admin-request-add-btn:hover {
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 8px 20px rgba(220, 38, 38, 0.4);
        color: white;
        text-decoration: none;
    }

    .admin-request-add-btn i {
        margin-right: 0.5rem;
        font-size: 1rem;
    }

    :root {
        --harrier-red: #dc2626;
        --harrier-dark: #1f2937;
    }
</style>
