<!-- Resource Creation Modal -->
<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4" id="resource-create-modal">
    <div class="modal-panel w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <!-- Modal Header -->
        <div class="modal-header flex items-center justify-between p-6 border-b border-gray-200">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-harrier-red bg-opacity-10 rounded-full flex items-center justify-center">
                    <i class="fas fa-plus text-harrier-red text-lg"></i>
                </div>
                <div>
                    <h2 class="text-xl font-bold text-harrier-dark font-montserrat">Create New Content</h2>
                    <p class="text-sm text-gray-600 font-raleway">Add new {{ content_type|title }} to your resource library</p>
                </div>
            </div>
            <button type="button" 
                    class="text-gray-400 hover:text-gray-600 transition-colors"
                    onclick="closeModal('resource-create-modal')">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <!-- Modal Body -->
        <div class="p-6">
            <form id="resource-create-form"
                  hx-post="{% url 'core:admin_resource_create' %}"
                  hx-encoding="multipart/form-data"
                  hx-target="#resource-create-modal"
                  hx-swap="outerHTML">
                {% csrf_token %}
                
                <!-- Basic Information Section -->
                <div class="form-section mb-6">
                    <h3 class="form-section-title">
                        <i class="fas fa-info-circle mr-2 text-harrier-red"></i>
                        Basic Information
                    </h3>
                    
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Title -->
                        <div class="form-group lg:col-span-2">
                            <label class="form-label">
                                <i class="fas fa-heading text-harrier-red"></i>
                                Title *
                            </label>
                            <input type="text" 
                                   name="title" 
                                   required
                                   class="form-input"
                                   placeholder="Enter content title">
                        </div>

                        <!-- Content Type -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-tag text-harrier-red"></i>
                                Content Type *
                            </label>
                            <select name="content_type" required class="form-input">
                                {% for value, label in content_type_choices %}
                                    <option value="{{ value }}" {% if content_type == value %}selected{% endif %}>{{ label }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- Category -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-folder text-harrier-red"></i>
                                Category
                            </label>
                            <select name="category" class="form-input">
                                <option value="">Select Category</option>
                                {% for category in categories %}
                                    <option value="{{ category.id }}">{{ category.name }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- Excerpt -->
                        <div class="form-group lg:col-span-2">
                            <label class="form-label">
                                <i class="fas fa-quote-left text-harrier-red"></i>
                                Excerpt
                            </label>
                            <textarea name="excerpt" 
                                      rows="3" 
                                      class="form-input"
                                      placeholder="Brief summary of the content"></textarea>
                        </div>
                    </div>
                </div>

                <!-- Content Section -->
                <div class="form-section mb-6">
                    <h3 class="form-section-title">
                        <i class="fas fa-edit mr-2 text-harrier-red"></i>
                        Content
                    </h3>
                    
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-file-alt text-harrier-red"></i>
                            Content *
                        </label>
                        <textarea name="content" 
                                  required
                                  rows="12" 
                                  class="form-input"
                                  placeholder="Write your content here..."></textarea>
                        <p class="text-xs text-gray-500 mt-1">You can use HTML formatting in your content.</p>
                    </div>
                </div>

                <!-- Media Section -->
                <div class="form-section mb-6">
                    <h3 class="form-section-title">
                        <i class="fas fa-image mr-2 text-harrier-red"></i>
                        Media
                    </h3>
                    
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Featured Image -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-camera text-harrier-red"></i>
                                Featured Image
                            </label>
                            <input type="file" 
                                   name="featured_image" 
                                   accept="image/*"
                                   class="form-input">
                            <p class="text-xs text-gray-500 mt-1">Recommended size: 1200x630px</p>
                        </div>

                        <!-- Featured Image Alt Text -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-eye text-harrier-red"></i>
                                Image Alt Text
                            </label>
                            <input type="text" 
                                   name="featured_image_alt" 
                                   class="form-input"
                                   placeholder="Describe the image for accessibility">
                        </div>

                        <!-- Video URL -->
                        <div class="form-group lg:col-span-2">
                            <label class="form-label">
                                <i class="fas fa-video text-harrier-red"></i>
                                Video URL
                            </label>
                            <input type="url" 
                                   name="video_url" 
                                   class="form-input"
                                   placeholder="YouTube or Vimeo URL">
                        </div>
                    </div>
                </div>

                <!-- Settings Section -->
                <div class="form-section mb-6">
                    <h3 class="form-section-title">
                        <i class="fas fa-cog mr-2 text-harrier-red"></i>
                        Settings
                    </h3>
                    
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- Difficulty Level -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-signal text-harrier-red"></i>
                                Difficulty Level
                            </label>
                            <select name="difficulty_level" class="form-input">
                                <option value="">Select Level</option>
                                {% for value, label in difficulty_choices %}
                                    <option value="{{ value }}">{{ label }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- Estimated Read Time -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-clock text-harrier-red"></i>
                                Read Time (minutes)
                            </label>
                            <input type="number" 
                                   name="estimated_read_time" 
                                   min="1" 
                                   max="120"
                                   value="5"
                                   class="form-input">
                        </div>

                        <!-- Tags -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-tags text-harrier-red"></i>
                                Tags
                            </label>
                            <select name="tags" multiple class="form-input" size="3">
                                {% for tag in tags %}
                                    <option value="{{ tag.id }}">{{ tag.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>

                <!-- SEO Section -->
                <div class="form-section mb-6">
                    <h3 class="form-section-title">
                        <i class="fas fa-search mr-2 text-harrier-red"></i>
                        SEO Settings
                    </h3>
                    
                    <div class="grid grid-cols-1 gap-6">
                        <!-- Meta Description -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-file-text text-harrier-red"></i>
                                Meta Description
                            </label>
                            <textarea name="meta_description" 
                                      rows="2" 
                                      maxlength="160"
                                      class="form-input"
                                      placeholder="Brief description for search engines (max 160 characters)"></textarea>
                        </div>

                        <!-- Meta Keywords -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-key text-harrier-red"></i>
                                Meta Keywords
                            </label>
                            <input type="text" 
                                   name="meta_keywords" 
                                   class="form-input"
                                   placeholder="Comma-separated keywords">
                        </div>
                    </div>
                </div>

                <!-- Publishing Options -->
                <div class="form-section mb-6">
                    <h3 class="form-section-title">
                        <i class="fas fa-globe mr-2 text-harrier-red"></i>
                        Publishing Options
                    </h3>
                    
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div class="form-group">
                            <label class="flex items-center space-x-3">
                                <input type="checkbox" 
                                       name="is_published" 
                                       class="rounded border-gray-300 text-harrier-red focus:ring-harrier-red">
                                <span class="form-label mb-0">
                                    <i class="fas fa-eye text-harrier-red"></i>
                                    Publish immediately
                                </span>
                            </label>
                        </div>

                        <div class="form-group">
                            <label class="flex items-center space-x-3">
                                <input type="checkbox" 
                                       name="is_featured" 
                                       class="rounded border-gray-300 text-harrier-red focus:ring-harrier-red">
                                <span class="form-label mb-0">
                                    <i class="fas fa-star text-harrier-red"></i>
                                    Mark as featured
                                </span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Modal Footer -->
                <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                    <button type="button" 
                            class="enhanced-btn enhanced-btn-secondary"
                            onclick="closeModal('resource-create-modal')">
                        <i class="fas fa-times mr-2"></i>
                        <span>Cancel</span>
                    </button>
                    <button type="submit" 
                            class="enhanced-btn enhanced-btn-save">
                        <i class="fas fa-save mr-2"></i>
                        <span>Create Content</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
    /* Enhanced Modal Styles */
    .modal-panel {
        backdrop-filter: blur(20px);
        background: rgba(255, 255, 255, 0.98);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        border-radius: 16px;
    }

    .modal-header {
        background: linear-gradient(135deg, #F9FAFB 0%, #F3F4F6 100%);
        border-radius: 16px 16px 0 0;
    }

    /* Form Section Styles */
    .form-section {
        background: rgba(249, 250, 251, 0.5);
        border: 1px solid rgba(229, 231, 235, 0.8);
        border-radius: 12px;
        padding: 1.5rem;
    }

    .form-section-title {
        font-size: 1rem;
        font-weight: 600;
        color: var(--harrier-dark);
        font-family: 'Montserrat', sans-serif;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
    }

    /* Enhanced Form Styling */
    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        display: block;
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--harrier-dark);
        margin-bottom: 0.5rem;
        font-family: 'Montserrat', sans-serif;
        letter-spacing: 0.025em;
    }

    .form-label i {
        margin-right: 8px;
        width: 16px;
        height: 16px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
    }

    .form-input {
        width: 100%;
        padding: 12px 16px;
        border: 2px solid #E5E7EB;
        border-radius: 8px;
        font-size: 14px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        font-family: 'Raleway', sans-serif;
    }

    .form-input:focus {
        outline: none;
        border-color: var(--harrier-red);
        box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
        background: rgba(255, 255, 255, 1);
    }

    .form-input::placeholder {
        color: #9CA3AF;
        font-style: italic;
    }

    /* Enhanced Button Base Styles */
    .enhanced-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 12px 20px;
        font-weight: 600;
        font-size: 14px;
        line-height: 1.2;
        border-radius: 10px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translateY(0);
        font-family: 'Montserrat', sans-serif;
        min-width: 120px;
        max-width: 180px;
        height: 44px;
        border: 2px solid transparent;
        cursor: pointer;
        user-select: none;
        position: relative;
        overflow: visible;
        white-space: nowrap;
        box-sizing: border-box;
    }

    .enhanced-btn:hover {
        transform: translateY(-2px);
    }

    .enhanced-btn:active {
        transform: translateY(0);
    }

    /* Secondary Button Styles */
    .enhanced-btn-secondary {
        background: rgba(107, 114, 128, 0.1);
        color: #374151;
        border: 1px solid rgba(107, 114, 128, 0.2);
        backdrop-filter: blur(10px);
    }

    .enhanced-btn-secondary:hover {
        background: rgba(107, 114, 128, 0.15);
        border-color: rgba(107, 114, 128, 0.3);
        box-shadow: 0 8px 24px rgba(107, 114, 128, 0.2);
    }

    /* Save Button Styles */
    .enhanced-btn-save {
        background: linear-gradient(135deg,
            var(--harrier-red) 0%,
            #dc2626 15%,
            var(--harrier-red) 35%,
            #b91c1c 65%,
            var(--harrier-red) 85%,
            #dc2626 100%);
        background-size: 300% 100%;
        background-position: 0% 50%;
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow:
            0 8px 24px rgba(220, 38, 38, 0.4),
            0 4px 12px rgba(185, 28, 28, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2),
            inset 0 -1px 0 rgba(0, 0, 0, 0.2);
        position: relative;
    }

    .enhanced-btn-save:hover {
        background-position: 100% 50%;
        transform: translateY(-3px) scale(1.02);
        box-shadow:
            0 12px 32px rgba(220, 38, 38, 0.5),
            0 6px 16px rgba(185, 28, 28, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.3),
            inset 0 -1px 0 rgba(0, 0, 0, 0.3);
    }
</style>

<script>
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.remove();
    }
}

// Handle form submission response
document.addEventListener('htmx:afterRequest', function(event) {
    if (event.target.id === 'resource-create-form') {
        try {
            const responseText = event.detail.xhr.responseText.trim();
            // Only try to parse as JSON if it looks like JSON
            if (responseText.startsWith('{') && responseText.endsWith('}')) {
                const response = JSON.parse(responseText);
                if (response.success) {
                    // Show enhanced success toast
                    if (window.showResourceSuccess) {
                        showResourceSuccess(response.message, {
                            title: 'Content Created',
                            action: {
                                text: 'View',
                                handler: () => {
                                    // Refresh the current tab to show new content
                                    const activeTab = document.querySelector('.resource-tab.active');
                                    if (activeTab) {
                                        htmx.trigger(activeTab, 'click');
                                    }
                                }
                            }
                        });
                    } else {
                        showToast(response.message, 'success');
                    }

                closeModal('resource-create-modal');

                // Refresh the current tab
                const activeTab = document.querySelector('.resource-tab.active');
                if (activeTab) {
                    htmx.trigger(activeTab, 'click');
                }
            } else {
                // Show enhanced error toast
                if (window.showResourceError) {
                    showResourceError(response.message || 'Error creating content', {
                        title: 'Creation Failed',
                        action: {
                            text: 'Retry',
                            handler: () => {
                                // Keep modal open for retry
                            },
                            dismissOnClick: false
                        }
                    });
                } else {
                    showToast(response.message || 'Error creating content', 'error');
                }
            }
        } catch (e) {
            // Handle JSON parse errors
            if (window.showResourceError) {
                showResourceError('Invalid response from server. Please try again.', {
                    title: 'Server Error'
                });
            } else {
                showToast('Invalid response from server. Please try again.', 'error');
            }
        }
    }
});
</script>
