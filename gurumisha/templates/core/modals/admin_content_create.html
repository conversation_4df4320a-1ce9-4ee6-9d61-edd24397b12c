<!-- Enhanced Content Creation Modal with <PERSON>rrier Design -->
<div class="fixed inset-0 z-50 overflow-y-auto" 
     x-data="{ show: false, isSubmitting: false }"
     x-init="show = true"
     x-show="show"
     x-transition:enter="ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0">
    
    <!-- Modal Backdrop -->
    <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
         @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)"></div>
    
    <!-- Modal Container -->
    <div class="flex min-h-full items-center justify-center p-4 text-center sm:p-0">
        
        <!-- Modal Panel -->
        <div class="modal-panel relative transform overflow-hidden rounded-2xl text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-4xl"
             x-show="show"
             x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
             x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
            
            <!-- Modal Header -->
            <div class="modal-header px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-gradient-to-br from-harrier-red to-harrier-dark rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-plus text-white"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-harrier-dark font-montserrat">Create New Content</h3>
                            <p class="text-sm text-gray-600 font-raleway">Add new content post to your website</p>
                        </div>
                    </div>
                    <button type="button" 
                            class="text-gray-400 hover:text-gray-600 transition-colors"
                            @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            
            <!-- Modal Body -->
            <div class="modal-body p-6 max-h-[70vh] overflow-y-auto">
                <form hx-post="{% url 'core:admin_content_create' %}"
                      hx-target="#tab-content"
                      hx-swap="innerHTML"
                      @submit="isSubmitting = true"
                      class="space-y-6">
                    {% csrf_token %}
                    
                    <!-- Basic Information Section -->
                    <div class="form-section">
                        <h4 class="form-section-title">
                            <i class="fas fa-info-circle mr-2"></i>Basic Information
                        </h4>
                        
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- Title -->
                            <div class="form-group lg:col-span-2">
                                <label class="form-label">
                                    <i class="fas fa-heading text-harrier-red"></i>
                                    Title <span class="text-red-500">*</span>
                                </label>
                                <input type="text" 
                                       name="title" 
                                       required
                                       class="form-input"
                                       placeholder="Enter content title">
                            </div>
                            
                            <!-- Content Type -->
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-tag text-harrier-blue"></i>
                                    Content Type <span class="text-red-500">*</span>
                                </label>
                                <select name="content_type" required class="form-select">
                                    {% for value, label in content_types %}
                                    <option value="{{ value }}">{{ label }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <!-- Category -->
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-folder text-purple-600"></i>
                                    Category
                                </label>
                                <select name="category" class="form-select">
                                    <option value="">Select Category</option>
                                    {% for category in categories %}
                                    <option value="{{ category.id }}">{{ category.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Content Section -->
                    <div class="form-section">
                        <h4 class="form-section-title">
                            <i class="fas fa-edit mr-2"></i>Content
                        </h4>
                        
                        <!-- Excerpt -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-quote-left text-green-600"></i>
                                Excerpt
                            </label>
                            <textarea name="excerpt" 
                                      rows="3" 
                                      class="form-textarea"
                                      placeholder="Brief summary of the content"></textarea>
                        </div>
                        
                        <!-- Main Content -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-file-alt text-harrier-dark"></i>
                                Content <span class="text-red-500">*</span>
                            </label>
                            <textarea name="content" 
                                      rows="12" 
                                      required
                                      class="form-textarea"
                                      placeholder="Enter your content here..."></textarea>
                        </div>
                    </div>
                    
                    <!-- Tags and Settings Section -->
                    <div class="form-section">
                        <h4 class="form-section-title">
                            <i class="fas fa-cogs mr-2"></i>Settings & Tags
                        </h4>
                        
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- Tags -->
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-tags text-orange-600"></i>
                                    Tags
                                </label>
                                <select name="tags" multiple class="form-select" size="4">
                                    {% for tag in tags %}
                                    <option value="{{ tag.id }}">{{ tag.name }}</option>
                                    {% endfor %}
                                </select>
                                <p class="form-help">Hold Ctrl/Cmd to select multiple tags</p>
                            </div>
                            
                            <!-- Publishing Options -->
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-eye text-blue-600"></i>
                                    Publishing Options
                                </label>
                                <div class="space-y-3">
                                    <label class="form-checkbox-label">
                                        <input type="checkbox" name="is_published" class="form-checkbox">
                                        <span>Publish immediately</span>
                                    </label>
                                    <label class="form-checkbox-label">
                                        <input type="checkbox" name="is_featured" class="form-checkbox">
                                        <span>Mark as featured</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="flex flex-col sm:flex-row gap-4 justify-center mt-6 pt-6 border-t border-gray-200">
                        <!-- Cancel Button -->
                        <button type="button"
                                class="enhanced-btn enhanced-btn-cancel"
                                @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)">
                            <i class="fas fa-times mr-2"></i>
                            <span>Cancel</span>
                        </button>

                        <!-- Submit Button -->
                        <button type="submit"
                                class="enhanced-btn enhanced-btn-submit"
                                :disabled="isSubmitting">
                            <i class="fas fa-plus mr-2" x-show="!isSubmitting"></i>
                            <i class="fas fa-spinner fa-spin mr-2" x-show="isSubmitting"></i>
                            <span x-text="isSubmitting ? 'Creating Content...' : 'Create Content'"></span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
    /* Enhanced Modal Styles */
    .modal-panel {
        backdrop-filter: blur(20px);
        background: rgba(255, 255, 255, 0.98);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }

    .modal-header {
        background: linear-gradient(135deg, #F9FAFB 0%, #F3F4F6 100%);
    }

    /* Form Section Styles */
    .form-section {
        background: rgba(249, 250, 251, 0.5);
        border: 1px solid rgba(229, 231, 235, 0.8);
        border-radius: 12px;
        padding: 1.5rem;
    }

    .form-section-title {
        font-size: 1rem;
        font-weight: 600;
        color: var(--harrier-dark);
        font-family: 'Montserrat', sans-serif;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
    }

    /* Enhanced Form Styling */
    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        display: block;
        font-size: 0.875rem;
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
        font-family: 'Montserrat', sans-serif;
        display: flex;
        align-items: center;
    }

    .form-label i {
        margin-right: 0.5rem;
        width: 16px;
        text-align: center;
    }

    .form-input,
    .form-select,
    .form-textarea {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid #E5E7EB;
        border-radius: 8px;
        font-size: 0.875rem;
        font-family: 'Raleway', sans-serif;
        transition: all 0.3s ease;
        background: white;
    }

    .form-input:focus,
    .form-select:focus,
    .form-textarea:focus {
        outline: none;
        border-color: var(--harrier-red);
        box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
    }

    .form-checkbox-label {
        display: flex;
        align-items: center;
        font-size: 0.875rem;
        color: #374151;
        font-family: 'Raleway', sans-serif;
    }

    .form-checkbox {
        margin-right: 0.5rem;
        border-radius: 4px;
        border: 2px solid #E5E7EB;
        color: var(--harrier-red);
    }

    .form-help {
        font-size: 0.75rem;
        color: #6B7280;
        margin-top: 0.25rem;
        font-family: 'Raleway', sans-serif;
    }

    /* Enhanced Button Styles (matching admin_request_add template) */
    .enhanced-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 12px 20px;
        font-weight: 600;
        font-size: 14px;
        line-height: 1.2;
        border-radius: 10px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translateY(0);
        font-family: 'Montserrat', sans-serif;
        min-width: 120px;
        max-width: 180px;
        height: 44px;
        border: 2px solid transparent;
        cursor: pointer;
        user-select: none;
        position: relative;
        overflow: visible;
        white-space: nowrap;
        box-sizing: border-box;
    }

    .enhanced-btn-cancel {
        background: linear-gradient(135deg, #F3F4F6 0%, #E5E7EB 100%);
        color: #374151;
        border: 1px solid #D1D5DB;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .enhanced-btn-cancel:hover {
        background: linear-gradient(135deg, #E5E7EB 0%, #D1D5DB 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    .enhanced-btn-submit {
        background: linear-gradient(135deg,
            var(--harrier-red-600) 0%,
            var(--harrier-red-500) 15%,
            var(--harrier-red-700) 35%,
            var(--harrier-dark-800) 65%,
            var(--harrier-dark-700) 85%,
            var(--harrier-red-800) 100%);
        background-size: 300% 100%;
        background-position: 0% 50%;
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow:
            0 8px 24px rgba(220, 38, 38, 0.4),
            0 4px 12px rgba(31, 41, 55, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2),
            inset 0 -1px 0 rgba(0, 0, 0, 0.2);
        position: relative;
    }

    .enhanced-btn-submit:hover {
        background-position: 100% 50%;
        transform: translateY(-2px);
        box-shadow:
            0 12px 32px rgba(220, 38, 38, 0.5),
            0 6px 16px rgba(31, 41, 55, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.3),
            inset 0 -1px 0 rgba(0, 0, 0, 0.3);
    }

    .enhanced-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none !important;
    }
</style>
