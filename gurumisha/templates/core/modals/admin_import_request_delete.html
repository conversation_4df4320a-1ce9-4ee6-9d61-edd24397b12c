{% load static %}

<!-- Delete Import Request Confirmation Modal -->
<div class="fixed inset-0 z-50 overflow-y-auto" 
     id="delete-import-request-modal"
     x-data="{ show: false }"
     x-init="show = true"
     x-show="show"
     x-transition:enter="ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0">
    
    <!-- Modal Backdrop -->
    <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity modal-backdrop"
         @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)"></div>
    
    <!-- Modal Container -->
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        
        <!-- Modal Panel -->
        <div class="inline-block align-bottom bg-white rounded-xl text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
             x-show="show"
             x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
             x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
            
            <!-- Modal Header -->
            <div class="bg-white px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-exclamation-triangle text-white"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-harrier-dark font-montserrat">
                                Delete Import Request
                            </h3>
                            <p class="text-sm text-gray-600 font-raleway">This action cannot be undone</p>
                        </div>
                    </div>
                    
                    <!-- Close Button -->
                    <button type="button" 
                            class="text-gray-400 hover:text-gray-600 transition-colors"
                            @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)">
                        <span class="sr-only">Close</span>
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            
            <!-- Modal Body -->
            <div class="bg-white px-6 py-6">
                <!-- Warning Icon and Message -->
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-lg font-semibold text-gray-900 mb-2">
                            Are you sure you want to delete this import request?
                        </h4>
                        <p class="text-gray-600 mb-4">
                            This will permanently delete the import request and all associated data. This action cannot be undone.
                        </p>
                        
                        <!-- Request Details Summary -->
                        <div class="bg-gray-50 rounded-lg p-4 mb-4">
                            <h5 class="font-semibold text-gray-900 mb-2">Request Details:</h5>
                            <div class="space-y-1 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Request ID:</span>
                                    <span class="font-medium">#{{ import_request.id|stringformat:"05d" }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Customer:</span>
                                    <span class="font-medium">{{ import_request.customer.get_full_name|default:import_request.customer.username }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Vehicle:</span>
                                    <span class="font-medium">{{ import_request.year }} {{ import_request.brand }} {{ import_request.model }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Status:</span>
                                    <span class="font-medium">{{ import_request.get_status_display }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Created:</span>
                                    <span class="font-medium">{{ import_request.created_at|date:"M d, Y" }}</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Warning Messages -->
                        <div class="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
                            <div class="flex items-start">
                                <i class="fas fa-exclamation-circle text-red-500 mt-0.5 mr-2"></i>
                                <div class="text-sm text-red-700">
                                    <p class="font-semibold mb-1">Warning:</p>
                                    <ul class="list-disc list-inside space-y-1">
                                        <li>All request data will be permanently deleted</li>
                                        <li>Customer will no longer be able to track this request</li>
                                        {% if import_request.status != 'pending' %}
                                        <li>This request has progressed beyond pending status</li>
                                        {% endif %}
                                        {% if import_request.tracking_number %}
                                        <li>Tracking number {{ import_request.tracking_number }} will be invalidated</li>
                                        {% endif %}
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Confirmation Checkbox -->
                        <div class="flex items-start space-x-3">
                            <input type="checkbox" 
                                   id="confirm-delete" 
                                   class="mt-1 w-4 h-4 text-red-600 bg-gray-100 border-gray-300 rounded focus:ring-red-500 focus:ring-2"
                                   x-data="{ checked: false }"
                                   x-model="checked"
                                   @change="$refs.deleteBtn.disabled = !checked">
                            <label for="confirm-delete" class="text-sm text-gray-700">
                                I understand that this action is permanent and cannot be undone. I want to delete this import request.
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Modal Footer -->
            <div class="bg-gray-50 px-6 py-4 flex justify-end space-x-3">
                <button type="button"
                        class="enhanced-btn enhanced-btn-cancel"
                        @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)">
                    <i class="fas fa-times mr-2"></i>
                    <span>Cancel</span>
                </button>
                <button type="button"
                        x-ref="deleteBtn"
                        disabled
                        class="enhanced-btn enhanced-btn-danger"
                        hx-delete="{% url 'core:admin_import_request_delete' import_request.id %}"
                        hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'
                        hx-target="#import-requests-table"
                        hx-swap="outerHTML"
                        hx-on::after-request="if(event.detail.successful) { show = false; setTimeout(() => $el.closest('.fixed').remove(), 200); }">
                    <i class="fas fa-trash mr-2"></i>
                    <span>Delete Request</span>
                </button>
            </div>
        </div>
    </div>
</div>

<style>
    /* Delete Modal Specific Styles */
    .delete-modal .modal-panel {
        border: 2px solid #FEE2E2;
    }
    
    .delete-modal .modal-header {
        background: linear-gradient(135deg, #FEF2F2 0%, #FEE2E2 100%);
    }
    
    /* Enhanced Button Base Styles */
    .enhanced-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 12px 20px;
        font-weight: 600;
        font-size: 14px;
        line-height: 1.2;
        border-radius: 10px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translateY(0);
        font-family: 'Montserrat', sans-serif;
        min-width: 120px;
        max-width: 180px;
        height: 44px;
        border: 2px solid transparent;
        cursor: pointer;
        user-select: none;
        position: relative;
        overflow: visible;
        white-space: nowrap;
        box-sizing: border-box;
    }

    /* Simplified Cancel Button */
    .enhanced-btn-cancel {
        background: linear-gradient(135deg, #f9fafb, #f3f4f6);
        color: #374151;
        border: 2px solid #e5e7eb;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }

    .enhanced-btn-cancel:hover {
        background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
        color: #1f2937;
        border-color: #d1d5db;
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
    }

    .enhanced-btn-cancel:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(156, 163, 175, 0.3);
    }

    .enhanced-btn-cancel:active {
        transform: translateY(0);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    /* Danger/Delete Button */
    .enhanced-btn-danger {
        background: linear-gradient(135deg, #dc2626, #b91c1c, #991b1b);
        background-size: 200% 100%;
        background-position: 0% 50%;
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 24px rgba(220, 38, 38, 0.4);
    }

    .enhanced-btn-danger:hover {
        background-position: 100% 50%;
        transform: translateY(-3px);
        box-shadow: 0 12px 30px rgba(220, 38, 38, 0.6);
    }

    .enhanced-btn-danger:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(248, 113, 113, 0.4);
    }

    .enhanced-btn-danger:active {
        transform: translateY(-1px);
        box-shadow: 0 6px 16px rgba(220, 38, 38, 0.4);
    }

    /* Disabled button styles */
    .modal-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none !important;
        box-shadow: none !important;
    }

    .modal-btn:disabled:hover {
        transform: none !important;
        box-shadow: none !important;
    }

    /* Checkbox animation */
    input[type="checkbox"]:checked {
        animation: checkboxPulse 0.3s ease-in-out;
    }

    @keyframes checkboxPulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }
</style>
